import React, { useState } from "react";
import {
  Modal,
  Upload,
  Button,
  Table,
  message,
  Spin,
  Space,
} from "antd";
import { UploadOutlined, DownloadOutlined } from "@ant-design/icons";
import * as XLSX from "xlsx";
import styles from "./import-modal.module.less";
import type { UploadFile } from "antd/es/upload/interface";

interface ImportModalProps {
  visible: boolean;
  examId: number;
  onClose: () => void;
  onSuccess?: () => void;
}

interface ImportError {
  row: number;
  field: string;
  message: string;
  value?: string;
}

interface ImportResult {
  import_log_id: number;
  total_count: number;
  success_count: number;
  error_count: number;
  errors: ImportError[];
}

export const ImportModal: React.FC<ImportModalProps> = ({
  visible,
  examId,
  onClose,
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [showResult, setShowResult] = useState(false);

  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning("请先选择文件");
      return;
    }

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append("file", fileList[0] as any);

      const response = await examApi.questionImport(examId, formData);
      if (response.code === 0) {
        setResult(response.data);
        setShowResult(true);

        if (response.data.error_count === 0) {
          message.success(`导入成功，共导入${response.data.success_count}道题目`);
          onSuccess?.();
          setTimeout(() => {
            onClose();
          }, 2000);
        } else if (response.data.success_count > 0) {
          message.warning(
            `导入完成，成功${response.data.success_count}条，失败${response.data.error_count}条，请检查错误详情`
          );
        } else {
          message.error("全部导入失败，请检查文件内容和格式");
        }
      } else {
        message.error(response.msg || "导入失败");
      }
    } catch (error: any) {
      message.error("导入失败，请重试");
      console.error("Import error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadTemplate = () => {
    // 创建Excel模板内容
    const templateData = [
      ["序号", "题目标题", "题型", "难度", "分数", "解析", "正确答案", "选项A", "选项B", "选项C", "选项D", "选项E", "选项F"],
      [1, "Java中用来声明常量的关键字是？", "single_choice", "medium", 5, "final关键字用于声明常量", "C", "var", "let", "final", "const", "", ""],
      [2, "Java的基本数据类型包括哪些？", "multiple_choice", "medium", 10, "Java有8种基本数据类型", "A,B,C,D,E,F", "byte", "short", "int", "long", "float", "double"],
      [3, "以下哪些是有效的Java标识符？", "single_choice", "easy", 3, "Java标识符命名规则", "A", "HelloWorld", "hello-world", "123abc", "class", "", ""],
      [4, "在Java中，以下哪些语句是正确的？", "multiple_choice", "medium", 8, "考察Java语法知识", "A,C,D", "int x = 10;", "int x = \"10\";", "String s = \"hello\";", "boolean b = true;", "", ""]
    ];

    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(templateData);

    // 设置列宽
    const colWidths = [
      { wch: 8 },   // 序号
      { wch: 40 },  // 题目标题
      { wch: 15 },  // 题型
      { wch: 8 },   // 难度
      { wch: 8 },   // 分数
      { wch: 30 },  // 解析
      { wch: 15 },  // 正确答案
      { wch: 20 },  // 选项A
      { wch: 20 },  // 选项B
      { wch: 20 },  // 选项C
      { wch: 20 },  // 选项D
      { wch: 20 },  // 选项E
      { wch: 20 }   // 选项F
    ];
    ws['!cols'] = colWidths;

    // 添加到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "questions");

    // 下载文件
    XLSX.writeFile(wb, "exam_questions_import_template.xlsx");
    message.success("模板下载成功");
  };

  const errorColumns = [
    {
      title: "行号",
      dataIndex: "row",
      width: 80,
    },
    {
      title: "错误信息",
      dataIndex: "message",
    },
    {
      title: "问题内容",
      dataIndex: "value",
      ellipsis: true,
    },
  ];

  return (
    <Modal
      title="导入试题"
      open={visible}
      onCancel={() => {
        onClose();
        setShowResult(false);
        setFileList([]);
        setResult(null);
      }}
      footer={null}
      width={700}
    >
      <Spin spinning={loading}>
        {!showResult ? (
          <div className={styles["import-container"]}>
            <div className={styles["upload-section"]}>
              <h3>第一步：下载导入模板</h3>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleDownloadTemplate}
                style={{ marginBottom: "20px" }}
              >
                下载Excel模板
              </Button>

              <h3>第二步：填写题目数据</h3>
              <p className={styles["help-text"]}>
                按照模板格式填写题目信息，支持单选、多选、填空、论述四种题型
              </p>

              <h3>第三步：选择并上传文件</h3>
              <Upload.Dragger
                name="file"
                multiple={false}
                beforeUpload={() => false}
                onChange={(info) => {
                  setFileList(info.fileList.slice(-1));
                }}
                accept=".xlsx,.xls"
              >
                <p className={styles["ant-upload-drag-icon"]}>
                  <UploadOutlined style={{ fontSize: 48, color: "#1890ff" }} />
                </p>
                <p className={styles["ant-upload-text"]}>
                  点击或拖拽Excel文件到此处上传
                </p>
                <p className={styles["ant-upload-hint"]}>
                  仅支持.xlsx格式，单个文件不超过10MB
                </p>
              </Upload.Dragger>

              {fileList.length > 0 && (
                <div className={styles["file-info"]}>
                  已选择文件：{fileList[0].name}
                </div>
              )}

              <div className={styles["actions"]}>
                <Space>
                  <Button
                    type="primary"
                    onClick={handleUpload}
                    loading={loading}
                  >
                    开始导入
                  </Button>
                  <Button onClick={onClose}>取消</Button>
                </Space>
              </div>
            </div>
          </div>
        ) : (
          <div className={styles["result-section"]}>
            <div className={styles["statistics"]}>
              <div className={styles["stat-item"]}>
                <div className={styles["stat-label"]}>总条数</div>
                <div className={styles["stat-value"]}>
                  {result?.total_count}
                </div>
              </div>
              <div
                className={[styles["stat-item"], styles["success"]].join(" ")}
              >
                <div className={styles["stat-label"]}>成功</div>
                <div className={styles["stat-value"]}>
                  {result?.success_count}
                </div>
              </div>
              <div className={[styles["stat-item"], styles["error"]].join(" ")}>
                <div className={styles["stat-label"]}>失败</div>
                <div className={styles["stat-value"]}>
                  {result?.error_count}
                </div>
              </div>
            </div>

            {result && result.error_count > 0 && (
              <div className={styles["error-section"]}>
                <h4>导入错误详情</h4>
                <Table
                  columns={errorColumns}
                  dataSource={result.errors}
                  size="small"
                  pagination={false}
                  rowKey={(_, index) => String(index)}
                  scroll={{ x: 600 }}
                />
              </div>
            )}

            <div className={styles["actions"]}>
              <Button
                type="primary"
                onClick={() => {
                  onClose();
                  setShowResult(false);
                  setFileList([]);
                  setResult(null);
                  onSuccess?.();
                }}
              >
                完成
              </Button>
            </div>
          </div>
        )}
      </Spin>
    </Modal>
  );
};
